name: Triggered Clean

on:
  workflow_dispatch:
    inputs:
      app_name:
        description: 'App name'
        required: true
        type: string
      branch:
        description: 'Branch name'
        required: true
        type: string

jobs:
  clean:
    runs-on: ubuntu-latest
    name: Clean Deleted Applications
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Clean deleted applications
        run: |
          ./auto/clean-application-by-trigger
        env:
          APP_NAME: ${{ inputs.app_name }}
          BRANCH: ${{ inputs.branch }}
