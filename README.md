# Deployment Manifests

This repo saves deployment manifests for our applications.

## Deployment status

|| Admin | Falcon | Falcon Beak | Web | Game | Zhu Rong | Question Service |
|---|---|---|---|---|---|---|---|
|Test|[![Admin](https://argocd.test.marsladder.com.au/api/badge?name=admin)](https://argocd.test.marsladder.com.au/applications/admin)|[![Falcon](https://argocd.test.marsladder.com.au/api/badge?name=falcon)](https://argocd.test.marsladder.com.au/applications/falcon)|[![<PERSON> Beak](https://argocd.test.marsladder.com.au/api/badge?name=falcon-beak)](https://argocd.test.marsladder.com.au/applications/falcon-beak)|[![Web](https://argocd.test.marsladder.com.au/api/badge?name=game)](https://argocd.test.marsladder.com.au/applications/game)|[![Game](https://argocd.test.marsladder.com.au/api/badge?name=game)](https://argocd.test.marsladder.com.au/applications/game)|[![Zhu Rong](https://argocd.test.marsladder.com.au/api/badge?name=zhurong)](https://argocd.test.marsladder.com.au/applications/zhurong)|[![Question Service](https://argocd.test.marsladder.com.au/api/badge?name=question-service)](https://argocd.test.marsladder.com.au/applications/question-service)|
|Prod|[![Admin](https://argocd.p.marsladder.com.au/api/badge?name=admin)](https://argocd.p.marsladder.com.au/applications/admin)|[![Falcon](https://argocd.p.marsladder.com.au/api/badge?name=falcon)](https://argocd.p.marsladder.com.au/applications/falcon)|[![Falcon Beak](https://argocd.p.marsladder.com.au/api/badge?name=falcon-beak)](https://argocd.p.marsladder.com.au/applications/falcon-beak)|[![Web](https://argocd.p.marsladder.com.au/api/badge?name=web)](https://argocd.p.marsladder.com.au/applications/web)|[![Game](https://argocd.p.marsladder.com.au/api/badge?name=game)](https://argocd.p.marsladder.com.au/applications/game)|[![Zhu Rong](https://argocd.p.marsladder.com.au/api/badge?name=zhurong)](https://argocd.p.marsladder.com.au/applications/zhurong)|[![Question Service](https://argocd.p.marsladder.com.au/api/badge?name=question-service)](https://argocd.p.marsladder.com.au/applications/question-service)|

## Prod deployment

please refer to manual release part in doc [CI & CD](https://github.com/firstedu-engineering/AWS-Infra-Management/blob/main/docs/ci-cd.md#manual-deployment).

## KMS encryption and decryption

We use KMS to encrypt the sensitive data, like db password, for our applications. Each env has a key, key info are saved in `conf/kms.conf`.

There are 2 auto scripts to help us doing the encryption and decryption jobs.

Use following script to encrypt your data, it could be plain text or a file.

```
./auto/encrypt ENV DATA|FILE
```

and this to decrypt it.

```
auto/decrypt ENCRYPTED_DATA
```

## Update Github Action Usage

Each time this repo is updated, we will report the github actions usage to NewRelic for further free time monitor.
