apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "{{ .Chart.Name }}"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "admin-{{ .Values.env }}"
    alb.ingress.kubernetes.io/group.order: "1000"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/inbound-cidrs: "{{ .Values.allowlist }}"
    alb.ingress.kubernetes.io/healthcheck-path: /vvvv.html
    alb.ingress.kubernetes.io/certificate-arn: "{{.Values.alb.certARN }}"
    alb.ingress.kubernetes.io/ssl-policy: "{{ .Values.alb.sslPolicy }}"
    alb.ingress.kubernetes.io/load-balancer-attributes: deletion_protection.enabled=true
    alb.ingress.kubernetes.io/load-balancer-attributes: routing.http2.enabled=true
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/auth-type: cognito
    alb.ingress.kubernetes.io/auth-on-unauthenticated-request: authenticate
    alb.ingress.kubernetes.io/auth-idp-cognito: >
      {"userPoolARN":"{{ .Values.alb.auth.userPoolARN }}","userPoolClientID":"{{ .Values.alb.auth.userPoolClientID }}","userPoolDomain":"{{ .Values.alb.auth.userPoolDomain }}"}
    alb.ingress.kubernetes.io/auth-scope: 'openid falcon/read falcon/write'
    alb.ingress.kubernetes.io/auth-session-timeout: '28800'

spec:
  rules:
    {{- range .Values.hosts.au }}
    - http:
        paths:
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: ssl-redirect
              port:
                name: use-annotation
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: "{{ $.Chart.Name }}-au"
              port:
                number: 80
      host: {{ . }}
    {{- end }}
