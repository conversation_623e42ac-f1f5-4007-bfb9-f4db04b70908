env: prod
allowlist: "************/32, *************/32, ***************/32, *************/32, *************/32"
# ************/32     HK AWS Bastion
# *************/32    FE AU Office
# ***************/32  FE AU Glen
# *************/32    HK ALI Bastion
# *************/32    FE SG Site
hosts:
  au:
    - admin.marsladder.com.au
    - admin.marsladder.com
alb:
  certARN: arn:aws:acm:ap-southeast-2:************:certificate/161106b5-b397-4dae-a084-3a9e5f260b77
  sslPolicy: ELBSecurityPolicy-TLS13-1-2-2021-06
  auth:
    userPoolARN: arn:aws:cognito-idp:ap-southeast-2:************:userpool/ap-southeast-2_iNYdIXKNh
    userPoolClientID: 1kce24glbbn58ch35u7lsqc9mh
    userPoolDomain: sso.marsladder.com.au

autoscaling:
  minReplicas: 2
  maxReplicas: 20

hpa:
  cpuThreshold: 200
  memThreshold: 80

environments:
  - name: APP_ENV
    value: "prod"
  - name: NEW_RELIC_ACCOUNT_ID
    value: "3308161"
  - name: NEW_RELIC_APPLICATION_ID
    value: "**********"
  - name: NEW_RELIC_LICENSE_KEY
    value: NRJS-a8789cb21748e4c1c30