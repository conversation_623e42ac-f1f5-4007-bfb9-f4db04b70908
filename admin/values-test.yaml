env: test
allowlist: "0.0.0.0/0"
hosts:
  au:
    - admin.test.marsladder.com.au
    - admin.test.marsladder.com
alb:
  certARN: arn:aws:acm:ap-southeast-2:************:certificate/01ac4076-0073-4ef8-a86a-75f2cdbe24ac
  sslPolicy: ELBSecurityPolicy-TLS13-1-2-2021-06
  auth:
    userPoolARN: arn:aws:cognito-idp:ap-southeast-2:************:userpool/ap-southeast-2_5XijcEWvj
    userPoolClientID: b10k4hdu5u71411ujh3iuun4a
    userPoolDomain: sso.test.marsladder.com.au

autoscaling:
  minReplicas: 1
  maxReplicas: 2

hpa:
  cpuThreshold: 500
  memThreshold: 900

environments:
  - name: APP_ENV
    value: "test"
  - name: NEW_RELIC_ACCOUNT_ID
    value: "3308161"
  - name: NEW_RELIC_APPLICATION_ID
    value: "**********"
  - name: NEW_RELIC_LICENSE_KEY
    value: NRJS-a8789cb21748e4c1c30

