env: prod
hosts:
  - ai-adapter.marsladder.com.au
  - ai-adapter.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/ai-adapter-service-role

autoscaling:
  minReplicas: 4
  maxReplicas: 20

resources:
  requests:
    memory: 4Gi
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 200
  memThreshold: 80

environments:
  - name: ACTIVE_PROFILE
    value: "prod"
  - name: NEW_RELIC_APP_NAME
    value: "AiAdapter(Prod)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEfOs8Km23SftKVy7jVRsdYAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOIdhGxgVjqkTeq6HAIBEIBDg/7F0akgGMlUWsFsveZ3U/Cbmbf4Cfk40EjgFckQJ5QYYnSVIxlkm6xu74udLhlo60ebrqog2PPz1xtmq/7bODhYDQ=="
  - name: KMS_ENCRYPTED_OPENAI_API_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEZdfYhaP2VCWlt3ozmlXjDAAABFzCCARMGCSqGSIb3DQEHBqCCAQQwggEAAgEAMIH6BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDBh2J1OjkRPLwVYdvgIBEICBzIhlidGrFamQnD92oJwBApGSkEwZKfoDh4PcoyrFmss83mCUAxBExZ3aXmNrliIuZtGXw5kGLx6vHyAhxayA04n26F+xCWHU6IUctGXG2/SOOhJsBXPVohGWnzWRkitqfRTIQnzFfAXPBIzIS7zmQZkWGe39+cSrIA9lqnXq/s6+zzesfDY6BpWl7FUkuEFj+BvrXMOciKiKarEAa7D7paxTWHy0mi9IMata2ni+GOhLIFCy+MsfUKfj42+WP5cDBvG7RTBba51nm8vN8A=="
  - name: MYSQL_HOST
    value: db.p.marsladder.com.au
  - name: MYSQL_USER
    value: kuixing
  - name: KMS_ENCRYPTED_MYSQL_PASSWORD
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAHewpPRwDNOwxs0/l/YYydxAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMWkxNnmXdpsfPBcqxAgEQgC9I9ZP1ceVHuXkw2cJYRd1v89H/ge8iEjuJ5NEPxRmCvpmZ+Z3XKruCBkblyIjXFw=="
  - name: CREDIT_SYSTEM_ENABLED
    value: "True"