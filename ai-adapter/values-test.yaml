env: test
hosts:
  - ai-adapter.test.marsladder.com.au
  - ai-adapter.test.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/ai-adapter-service-role

autoscaling:
  minReplicas: 1
  maxReplicas: 2

resources:
  requests:
    memory: 2Gi
    cpu: 500m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 400
  memThreshold: 300

environments:
  - name: ACTIVE_PROFILE
    value: "test"
  - name: NEW_RELIC_APP_NAME
    value: "AiAdapter(Test)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gF7H4sUYgyatDHyhHN4X0ouAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDNXBxv2TleaFWlDiVwIBEIBD3aVirGsENXnxFdzgWp3E8hVAaHQwcht62EGAfh3efArO7Gy+5aabWy85vyksIumbiCoNx/+gMxaINEFvVfXDDNBxgA=="
  - name: KMS_ENCRYPTED_OPENAI_API_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gGrf+M/hoLY/jB9A0C81HKYAAABFzCCARMGCSqGSIb3DQEHBqCCAQQwggEAAgEAMIH6BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDE3u/f/Ub6YIxxRDDAIBEICBzGWyjayONdr0pFv9Qbhx4DJsE4NIoG0NWYCwV66LoqUYoafD109APSD8Zh9veOyFOejqH5sPcsr6ol1eTl5nBUq4V6aLVABq5MNOO5wtBorIn8fI8FOf8zKeDnKvdyaqRkdRwiSy0JOfVwmHFaYrlyzaRaKKsU4KZcPCfkcHCPT33mx44RhWkvV7zKDigqu/+9cSuWurNxTORjx0W7ariJDLzoTOCUiTVxXbt2IZe3TJSbph6vRkFk8yd8xX4zjCObGcS/e6jmg4l0sepA=="
  - name: MYSQL_HOST
    value: db.test.marsladder.com.au
  - name: MYSQL_USER
    value: kuixing
  - name: KMS_ENCRYPTED_MYSQL_PASSWORD
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gFaPdwkf56+hivjmPaQVG5iAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMHmtjSoy915h+F5sLAgEQgC8enF1oRDvOzg/7FlgU7lVzZlou0HI7Ur9a3byguz8GTdh4wBczppn4lApKak0wig=="
  - name: CREDIT_SYSTEM_ENABLED
    value: "True"