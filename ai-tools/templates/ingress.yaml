apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "{{ .Chart.Name }}"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: /vvvv.html
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/auth-type: cognito
    alb.ingress.kubernetes.io/auth-on-unauthenticated-request: authenticate
    alb.ingress.kubernetes.io/auth-idp-cognito: >
      {"userPoolARN":"{{ .Values.alb.auth.userPoolARN }}","userPoolClientID":"{{ .Values.alb.auth.userPoolClientID }}","userPoolDomain":"{{ .Values.alb.auth.userPoolDomain }}"}
    alb.ingress.kubernetes.io/auth-scope: 'openid falcon/read falcon/write'
spec:
  rules:
    {{- range .Values.hosts.au }}
    - http:
        paths:
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: ssl-redirect
              port:
                name: use-annotation
        - path: /ai-tools*
          pathType: ImplementationSpecific
          backend:
            service:
              name: "{{ $.Chart.Name }}-au"
              port:
                number: 80
      host: {{ . }}
    {{- end }}
    {{- range .Values.hosts.com }}
    - http:
        paths:
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: ssl-redirect
              port:
                name: use-annotation
        - path: /ai-tools*
          pathType: ImplementationSpecific
          backend:
            service:
              name: "{{ $.Chart.Name }}-com"
              port:
                number: 80
      host: {{ . }}
    {{- end }}
