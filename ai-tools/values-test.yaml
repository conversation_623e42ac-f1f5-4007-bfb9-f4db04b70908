env: test
hosts:
  au:
    - teacher.test.marsladder.com.au
    - test.marsladder.com.au
  com:
    - teacher.test.marsladder.com
    - test.marsladder.com
base_domain:
  au: test.marsladder.com.au
  com: test.marsladder.com
alb:
  auth:
    userPoolARN: arn:aws:cognito-idp:ap-southeast-2:************:userpool/ap-southeast-2_5XijcEWvj
    userPoolClientID: 49ehnhud8d6aekdssc4llk55ou
    userPoolDomain: sso.test.marsladder.com.au

autoscaling:
  minReplicas: 1
  maxReplicas: 2

hpa:
  cpuThreshold: 500
  memThreshold: 450

environments:
  - name: GOOGLE_TAG_MANAGER_KEY
    value: GTM-M7M6X95
  - name: APP_ENV
    value: test
  - name: FAST_COMMENTS_TENANT_ID
    value: XSrXJR8zx2k
  - name: GROWTHBOOK_CLIENT_KEY
    value: sdk-z2zmqvOYvcoT86Rc
  - name: NEW_RELIC_ACCOUNT_ID
    value: "3308161"
  - name: NEW_RELIC_APPLICATION_ID
    value: "**********"
  - name: NEW_RELIC_LICENSE_KEY
    value: NRJS-a8789cb21748e4c1c30
