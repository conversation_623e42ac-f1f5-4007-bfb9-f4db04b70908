#!/bin/bash -e

cd $(dirname $0)/..

source ./conf/kms.conf
CONT=$2
ENV=$(echo $1 | tr '[:lower:]' '[:upper:]')
TEXT=""

if [[ $# -lt 2 ]]; then
	echo "No enough args!"
	echo "Usage: $0 ENV TOKEN|KEY_FILE"
	echo "  ENV: Environment flag, should be one of ci, test or prod,"
	echo "  TOKEN|KEY_FILE: Token or Keyfile."
	exit 1
fi

if [ ! -e ${CONT} ]; then
	echo "Did not detect such file '${CONT}', will treat it as plain text."
	TEXT=$(echo -n ${CONT} | base64)
else
	echo "Found file '${CONT}', will encrypt the file."
	TEXT="fileb://${CONT}"
fi

KEY="${ENV}_KMSKEY"

aws kms encrypt --key-id ${!KEY} --plaintext ${TEXT} --output text
