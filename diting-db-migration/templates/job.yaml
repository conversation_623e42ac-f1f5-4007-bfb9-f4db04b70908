apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ .Chart.Name }}-{{ .Values.image.tag }}"
  labels:
    app: "{{ .Chart.Name }}-{{ .Values.image.tag }}"
    release: "{{ .Values.image.tag }}"
spec:
  backoffLimit: 5
  activeDeadlineSeconds: 300
  parallelism: 1
  completions: 1
  template:
    spec:
      serviceAccountName: {{ .Chart.Name }}
      containers:
        - name: "{{ .Chart.Name }}"
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          env:
          {{- with .Values.environments }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
      restartPolicy: Never
