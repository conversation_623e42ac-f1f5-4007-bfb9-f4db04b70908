apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ .Chart.Name }}-au"
  labels:
    app: "{{ .Chart.Name }}-au"
    release: "{{ .Values.image.tag }}"
spec:
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: "{{ .Chart.Name }}-au"
  template:
    metadata:
      labels:
        app: "{{ .Chart.Name }}-au"
        release: "{{ .Values.image.tag }}"
    spec:
      serviceAccountName: "{{ .Chart.Name }}-au"
      containers:
        - name: "{{ .Chart.Name }}-au"
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: "{{ .Values.image.pullPolicy }}"
          resources:
            requests:
              memory: "{{ .Values.resources.requests.memory }}"
              cpu: "{{ .Values.resources.requests.cpu }}"
            limits:
              memory: "{{ .Values.resources.limits.memory }}"
              cpu: "{{ .Values.resources.limits.cpu }}"
          env:
            - name: GIT_HASH
              value: "{{ .Values.image.tag }}"
            - name: BASE_DOMAIN
              value: "{{ index .Values.hosts.au.web 0 }}"
            - name: NEW_RELIC_DISTRIBUTED_TRACING_ENABLED
              value: "true"
          {{- with .Values.environments }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: 4000
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 5
            httpGet:
              httpHeaders:
                - name: Host
                  value: "{{ index .Values.hosts.au.web 0 }}"
              path: /.well-known/apollo/server-health
              port: 4000
          readinessProbe:
            initialDelaySeconds: 5
            httpGet:
              httpHeaders:
                - name: Host
                  value: "{{ index .Values.hosts.au.web 0 }}"
              path: /.well-known/apollo/server-health
              port: 4000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ .Chart.Name }}-com"
  labels:
    app: "{{ .Chart.Name }}-com"
    release: "{{ .Values.image.tag }}"
spec:
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: "{{ .Chart.Name }}-com"
  template:
    metadata:
      labels:
        app: "{{ .Chart.Name }}-com"
        release: "{{ .Values.image.tag }}"
    spec:
      serviceAccountName: "{{ .Chart.Name }}-com"
      containers:
        - name: "{{ .Chart.Name }}-com"
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: "{{ .Values.image.pullPolicy }}"
          resources:
            requests:
              memory: "{{ .Values.resources.requests.memory }}"
              cpu: "{{ .Values.resources.requests.cpu }}"
            limits:
              memory: "{{ .Values.resources.limits.memory }}"
              cpu: "{{ .Values.resources.limits.cpu }}"
          env:
            - name: GIT_HASH
              value: "{{ .Values.image.tag }}"
            - name: BASE_DOMAIN
              value: "{{ index .Values.hosts.com.web 0 }}"
            - name: NEW_RELIC_DISTRIBUTED_TRACING_ENABLED
              value: "true"
          {{- with .Values.environments }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: 4000
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 5
            httpGet:
              httpHeaders:
                - name: Host
                  value: "{{ index .Values.hosts.com.web 0 }}"
              path: /.well-known/apollo/server-health
              port: 4000
          readinessProbe:
            initialDelaySeconds: 5
            httpGet:
              httpHeaders:
                - name: Host
                  value: "{{ index .Values.hosts.com.web 0 }}"
              path: /.well-known/apollo/server-health
              port: 4000
