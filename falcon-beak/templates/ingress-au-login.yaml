apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "{{ .Chart.Name }}-au-login"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/group.order: "750"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: '/.well-known/apollo/server-health'
    alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/auth-type: cognito
    alb.ingress.kubernetes.io/auth-on-unauthenticated-request: authenticate
    alb.ingress.kubernetes.io/auth-idp-cognito: '{"userPoolARN":"{{ .Values.alb.auth.userPoolARN }}","userPoolClientID":"{{ .Values.alb.auth.userPoolClientID }}","userPoolDomain":"{{ .Values.alb.auth.userPoolDomain }}"}'
    alb.ingress.kubernetes.io/auth-scope: 'openid falcon/read falcon/write'
    alb.ingress.kubernetes.io/auth-session-timeout: "21600"
spec:
  rules:
    {{- range .Values.hosts.au.web }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/login
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-au"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
    {{- range .Values.hosts.au.principal }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/principal-login
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-au"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "teacher-{{ .Chart.Name }}-au-login"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/group.order: "750"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: '/.well-known/apollo/server-health'
    alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/auth-type: cognito
    alb.ingress.kubernetes.io/auth-on-unauthenticated-request: authenticate
    alb.ingress.kubernetes.io/auth-idp-cognito: '{"userPoolARN":"{{ .Values.alb.auth.userPoolARN }}","userPoolClientID":"{{ .Values.alb.auth.teacherClientID }}","userPoolDomain":"{{ .Values.alb.auth.userPoolDomain }}"}'
    alb.ingress.kubernetes.io/auth-scope: 'openid falcon/read falcon/write'
spec:
  rules:
    {{- range .Values.hosts.au.teacher }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/teacher-login
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-au"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "realus-{{ .Chart.Name }}-au-login"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/group.order: "750"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: '/.well-known/apollo/server-health'
    alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/auth-type: cognito
    alb.ingress.kubernetes.io/auth-on-unauthenticated-request: authenticate
    alb.ingress.kubernetes.io/auth-idp-cognito: '{"userPoolARN":"{{ .Values.alb.auth.userPoolARN }}","userPoolClientID":"{{ .Values.alb.auth.realusClientID }}","userPoolDomain":"{{ .Values.alb.auth.userPoolDomain }}"}'
    alb.ingress.kubernetes.io/auth-scope: 'openid'
    alb.ingress.kubernetes.io/auth-session-timeout: "28800"
spec:
  rules:
    {{- range .Values.hosts.au.realus }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/realus-login
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-com"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "realus-{{ .Chart.Name }}-au-login1"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/group.order: "750"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: '/.well-known/apollo/server-health'
    alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/auth-type: cognito
    alb.ingress.kubernetes.io/auth-on-unauthenticated-request: authenticate
    alb.ingress.kubernetes.io/auth-idp-cognito: '{"userPoolARN":"{{ .Values.alb.auth.userPoolARN }}","userPoolClientID":"{{ .Values.alb.auth.realusClientID }}","userPoolDomain":"{{ .Values.alb.auth.userPoolDomain }}"}'
    alb.ingress.kubernetes.io/auth-scope: 'openid'
    alb.ingress.kubernetes.io/auth-session-timeout: "28800"
spec:
  rules:
    {{- range .Values.hosts.au.realus }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/login
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-com"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
