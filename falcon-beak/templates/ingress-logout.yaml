---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "{{ .Chart.Name }}-au-logout"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/group.order: "750"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-path: '/.well-known/apollo/server-health'
    alb.ingress.kubernetes.io/target-group-attributes: load_balancing.algorithm.type=least_outstanding_requests
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
spec:
  rules:
    {{- range .Values.hosts.au.web }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/logout
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-au"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
    {{- range .Values.hosts.au.teacher }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/teacher-logout
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-au"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
    {{- range .Values.hosts.au.principal }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/principal-logout
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-au"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
    {{- range .Values.hosts.au.realus }}
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation
          - path: /bff/realus-logout
            pathType: ImplementationSpecific
            backend:
              service:
                name: "{{ $.Chart.Name }}-au"
                port:
                  number: 80
      host: {{ . }}
    {{- end }}
