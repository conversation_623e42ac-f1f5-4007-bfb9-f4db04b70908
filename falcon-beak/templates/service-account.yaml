apiVersion: v1
kind: ServiceAccount
metadata:
  name: "{{ .Chart.Name }}-au"
  labels:
    app: "{{ .Chart.Name }}-au"
  annotations:
    eks.amazonaws.com/role-arn: "{{ .Values.serviceAccount.roleARN }}"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: "{{ .Chart.Name }}-com"
  labels:
    app: "{{ .Chart.Name }}-com"
  annotations:
    eks.amazonaws.com/role-arn: "{{ .Values.serviceAccount.roleARN }}"
