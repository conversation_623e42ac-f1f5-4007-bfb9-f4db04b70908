apiVersion: v1
kind: Service
metadata:
  name: "{{ .Chart.Name }}-au"
  labels:
    app: "{{ .Chart.Name }}-au"
    release: "{{ .Values.image.tag }}"
spec:
  type: "{{ .Values.service.type }}"
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 4000
      protocol: TCP
      name: http
  selector:
    app: "{{ .Chart.Name }}-au"
---
apiVersion: v1
kind: Service
metadata:
  name: "{{ .Chart.Name }}-com"
  labels:
    app: "{{ .Chart.Name }}-com"
    release: "{{ .Values.image.tag }}"
spec:
  type: "{{ .Values.service.type }}"
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 4000
      protocol: TCP
      name: http
  selector:
    app: "{{ .Chart.Name }}-com"
