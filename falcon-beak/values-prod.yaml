env: prod
hosts:
  au:
    web:
      - marsladder.com.au
    admin:
      - admin.marsladder.com.au
      - admin.marsladder.com
    teacher:
      - teacher.marsladder.com.au
    principal:
      - principal.marsladder.com.au
    realus:
      - www.realus.com.au
      - realus.com.au
  com:
    web:
      - marsladder.com
    teacher:
      - teacher.marsladder.com
    principal:
      - principal.marsladder.com
alb:
  auth:
    userPoolARN: arn:aws:cognito-idp:ap-southeast-2:************:userpool/ap-southeast-2_iNYdIXKNh
    userPoolClientID: 23au098hj9rjqushuran8d0a1b
    adminClientID: 1kce24glbbn58ch35u7lsqc9mh
    teacherClientID: 23au098hj9rjqushuran8d0a1b
    realusClientID: 39kdjjoa4jdhjdj42j4ajsupcd
    userPoolDomain: sso.marsladder.com.au
serviceAccount:
  roleARN: arn:aws:iam::************:role/falcon-beak-service-role

autoscaling:
  minReplicas: 2
  maxReplicas: 20

resources:
  requests:
    memory: 2Gi
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 200
  memThreshold: 80

environments:
  - name: AWS_DEFAULT_REGION
    value: "ap-southeast-2"
  - name: APP_ENV
    value: prod
  - name: NEW_RELIC_APP_NAME
    value: "Falcon-Beak(Prod)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEfOs8Km23SftKVy7jVRsdYAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOIdhGxgVjqkTeq6HAIBEIBDg/7F0akgGMlUWsFsveZ3U/Cbmbf4Cfk40EjgFckQJ5QYYnSVIxlkm6xu74udLhlo60ebrqog2PPz1xtmq/7bODhYDQ=="
  - name: TIKTOK_PIXEL_ID
    value: "CE4CHE3C77U118FB671G"
  - name: KMS_ENCRYPTED_TIKTOK_EVENT_API_TOKEN
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAF1LDzPgn+lKlZp0BxxTfGWAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDEpcJT6GrJyYYshd9wIBEIBDewq7YRarbN9bMQ98hpqM0vh5/s0XhYGk+FrDANqithKTY47KxaztWvhy1ii1p/WYAsJLkYefDRim9jSTM3gA8Kusxw=="
  - name: INVOICE_GENERATOR_API_KEY
    value: "sk_Gqpo4WCJKaafXs0UvJWCCTiKmoqfK8GK"
