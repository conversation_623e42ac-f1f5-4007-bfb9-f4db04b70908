env: test
hosts:
  au:
    web:
      - test.marsladder.com.au
    admin:
      - admin.test.marsladder.com.au
      - admin.test.marsladder.com
    teacher:
      - teacher.test.marsladder.com.au
    principal:
      - principal.test.marsladder.com.au
    realus:
      - www.test.realus.com.au
      - test.realus.com.au    
  com:
    web:
      - test.marsladder.com
    teacher:
      - teacher.test.marsladder.com
    principal:
      - principal.test.marsladder.com
alb:
  auth:
    userPoolARN: arn:aws:cognito-idp:ap-southeast-2:************:userpool/ap-southeast-2_5XijcEWvj
    userPoolClientID: 49ehnhud8d6aekdssc4llk55ou
    adminClientID: b10k4hdu5u71411ujh3iuun4a
    teacherClientID: 49ehnhud8d6aekdssc4llk55ou
    realusClientID: 7e82143tp4pbrkp66d8eidbcj8
    userPoolDomain: sso.test.marsladder.com.au
serviceAccount:
  roleARN: arn:aws:iam::************:role/falcon-beak-service-role

autoscaling:
  minReplicas: 1
  maxReplicas: 1

resources:
  requests:
    memory: 500m
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 800
  memThreshold: 1500

environments:
  - name: AWS_DEFAULT_REGION
    value: "ap-southeast-2"
  - name: APP_ENV
    value: test
  - name: NEW_RELIC_APP_NAME
    value: "Falcon-Beak(Test)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gF7H4sUYgyatDHyhHN4X0ouAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDNXBxv2TleaFWlDiVwIBEIBD3aVirGsENXnxFdzgWp3E8hVAaHQwcht62EGAfh3efArO7Gy+5aabWy85vyksIumbiCoNx/+gMxaINEFvVfXDDNBxgA=="
  - name: TIKTOK_PIXEL_ID
    value: "CDV2H33C77UDDKL3GDPG"
  - name: KMS_ENCRYPTED_TIKTOK_EVENT_API_TOKEN
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gGiOK73onh7yY1/HhS+ikqfAAAAhjCBgwYJKoZIhvcNAQcGoHYwdAIBADBvBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDHUyIqZ+RzEax6bTcgIBEIBCOMYvGRffxbmC0zPqu185xs0dIrSPXmRFPAA4VzN0fHv3+RUpuncTMvua1KxHhy3Ul5FMszTbCA/SuNwmy18VK4Kj"
  - name: INVOICE_GENERATOR_API_KEY
    value: "sk_Gqpo4WCJKaafXs0UvJWCCTiKmoqfK8GK"