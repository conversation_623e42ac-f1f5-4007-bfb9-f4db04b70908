env: prod
hosts:
  - api.p.marsladder.com.au
  - api.p.marsladder.com
  - api.marsladder.com.au
  - api.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/falcon-service-role

autoscaling:
  minReplicas: 2
  maxReplicas: 20

resources:
  requests:
    memory: 4Gi
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 200
  memThreshold: 80

environments:
  - name: JAVA_TOOL_OPTIONS
    value: "-XX:MaxMetaspaceSize=1024m"
  - name: SPRING_PROFILES_ACTIVE
    value: prod
  - name: SPRING_DATASOURCE_URL
    value: ***************************************************
  - name: SPRING_DATASOURCE_USERNAME
    value: ladygaga
  - name: ACTIVEMQ_USER
    value: furongjiejie
  - name: KMS_ENCRYPTED_ACTIVEMQ_PASSWORD
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAHWNiC3W25bSvhi4YWhtIuRAAAAczBxBgkqhkiG9w0BBwagZDBiAgEAMF0GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMU+rDcacpNAVpE3EoAgEQgDDReYGMhI6UBY87l0ei1CUg2xZMQlxryKxfJGohoShShn8evSkBQxpx+K4E8/NFK0M="
  - name: KMS_ENCRYPTED_SPRING_DATASOURCE_PASSWORD
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEtO6y/iqEyWXqEbpvXEpdvAAAAdDByBgkqhkiG9w0BBwagZTBjAgEAMF4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMhcKIaq9EqhbJ0CZmAgEQgDGduF2rJt/o1IVmCbJGriU/GAxsaCWNDfF45B6RsD2hhH+OELD7h129tnBkKwCcLPr4"
  - name: NEW_RELIC_APP_NAME
    value: "Falcon(Prod)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEfOs8Km23SftKVy7jVRsdYAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOIdhGxgVjqkTeq6HAIBEIBDg/7F0akgGMlUWsFsveZ3U/Cbmbf4Cfk40EjgFckQJ5QYYnSVIxlkm6xu74udLhlo60ebrqog2PPz1xtmq/7bODhYDQ=="
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_SECRET
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAH5Jowb1iLigjvwM70as9a3AAAAzjCBywYJKoZIhvcNAQcGoIG9MIG6AgEAMIG0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDPzdcuXXLDcRR9w/mAIBEICBhrMsSdV4WXIMwRTsanTL9BqSkU8QsWVvY/7/ZApGMxIoQk7OC6KH5yz1LhhCuF3tInUZK2t9feXk/2OHEst4onfKI6nN9SwgeHVDhSR/boK14bzOqEs4cb10PxFjBfff9xdk5RuH9QeppYMONaa6DlB+UcvPNSwU1iGzLiX6V7DJVoSITDWY"
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_WEBHOOK_SECRET
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAFKKqn2VyGVurrTsR33REd4AAAAhTCBggYJKoZIhvcNAQcGoHUwcwIBADBuBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDDJ+d1EDq4MXY6jkBAIBEIBBNKandafH5ZYbZLwO/cRq7nJ7x6jXRkNXt9KngKwK97EQev1eY2mGuCW1ebiLxMF53jHmIVshFTU/owWunOptmYw="
  - name: NVWA_HOST
    value: http://nvwa
  - name: KLAVIYO_HOST
    value: https://a.klaviyo.com
  - name: KMS_ENCRYPTED_KLAVIYO_PRIVATE_API_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEoZN9wleVWQR9nhlyBSJkXAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDNnSarg8rmR7oe/2BQIBEIBAmXU9FSkd3vhpJ9+qzYymSYn5qiZb0Y+1vB+ONa3k+jCsrkf8PRKPOmRmAnt3+AoMabexhu0DLdekU72PzMIkrA=="
  - name: KLAVIYO_MARSLADDER_USERS_ALL_LIST
    value: "U7Bp8u"
  - name: KMS_ENCRYPTED_FACEBOOK_APP_SECRET
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAG+pqFiF+sadiNqJM53U4yHAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMdEZOw5aozp1zTC/CAgEQgDtBvt7wZ7lP5N7p1vJHaQSV1RNORNkhOrwek6wRl3JHTRmvKzBabGORKvNcQYst7iUG2SMdR4zbPUx6iA=="
  - name: KMS_ENCRYPTED_FASTCOMMENTS_API_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAFLISHl5Wp4Q5zhGDUqWQagAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMNRUhfLP+5H/ZDeUyAgEQgDtnNylzqrklF6vHgNbNr6rrBV9lIypugj0+DzIOVkeWmt2XjQN9wTjqTfcSksrXGdId7XksEB/j7sbk+Q=="
  - name: FASTCOMMENTS_TENANT_ID
    value: "1k7qLQe9iAb"
  - name: SUBSCRIPTION_BUNDLE_TRIAL_COURSE_ENABLED
    value: "true"
