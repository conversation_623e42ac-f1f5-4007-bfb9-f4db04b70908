env: test
hosts:
  - api.test.marsladder.com.au
  - api.test.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/falcon-service-role

autoscaling:
  minReplicas: 1
  maxReplicas: 2

resources:
  requests:
    memory: 2Gi
    cpu: 500m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 400
  memThreshold: 300

environments:
  - name: JAVA_TOOL_OPTIONS
    value: "-XX:MaxMetaspaceSize=1024m"
  - name: SPRING_PROFILES_ACTIVE
    value: test
  - name: SPRING_DATASOURCE_URL
    value: ******************************************************
  - name: SPRING_DATASOURCE_USERNAME
    value: flyway
  - name: ACTIVEMQ_USER
    value: furongjiejie
  - name: KMS_ENCRYPTED_ACTIVEMQ_PASSWORD
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gGT6HxJoSVlVUBzHZeyDtwnAAAAczBxBgkqhkiG9w0BBwagZDBiAgEAMF0GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMNe66+pnMFvrIs5RCAgEQgDCE9QkYVKSe/h9jU4wv9blinMGPdDp7j52x2BwP79sAFHyC8YBpj4PniqSKzqp1FFU="
  - name: KMS_ENCRYPTED_SPRING_DATASOURCE_PASSWORD
    value: "AQICAHhHMXezALbBYgngtozRS/iXtUB3VCMhVjChtgwecP1ugAH9p/58zP7oGrdGpUuNtbeNAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMXokwRGWoVpQL6LEsAgEQgC9JwaaRO1mIAq03YGiPP38BcQC/6fhvQ9JPoSywJ0wj/5ZPTkIzzBTsUEh78YEwlA=="
  - name: NEW_RELIC_APP_NAME
    value: "Falcon(Test)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gF7H4sUYgyatDHyhHN4X0ouAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDNXBxv2TleaFWlDiVwIBEIBD3aVirGsENXnxFdzgWp3E8hVAaHQwcht62EGAfh3efArO7Gy+5aabWy85vyksIumbiCoNx/+gMxaINEFvVfXDDNBxgA=="
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_SECRET
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gHko+mKZvaFl5sYwxgg+HX4AAAAzjCBywYJKoZIhvcNAQcGoIG9MIG6AgEAMIG0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDGDsvifOw6VT+D8BzgIBEICBhk5QU1lK6dtBSkA6P+vFS1qUERgOJ/yCQLWUb659fF097ZgmsKgCONTEWN/caZZ7YHlpbIMQIfKngbH5krQHzTYFb++Lwwzk+2hulh7vImipVJDVgdGrmxZHMRnvCn9FJ5opQmN3YFD5LuCleTx6ZsdeB33Au7a3OBYb9LrsPHBm0BfPoMVI"
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_WEBHOOK_SECRET
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gGF8IGb03bqUvoRcYNFqCkLAAAAhTCBggYJKoZIhvcNAQcGoHUwcwIBADBuBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDHccpdA/Rs4GH/12PQIBEIBBfnfQ284MjNgjd9G/g7+GHQprD3ZHJk9dGfsC767HvueF10Fbh45040LIYiqz/q6eXuM1GV73qQJ8gK6pYD8BBA4="
  - name: NVWA_HOST
    value: http://nvwa
  - name: KLAVIYO_HOST
    value: https://a.klaviyo.com
  - name: KMS_ENCRYPTED_KLAVIYO_PRIVATE_API_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gEMJ1P9JZklUsOeW/PhMNWQAAAAhDCBgQYJKoZIhvcNAQcGoHQwcgIBADBtBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDG624h+Gf39sXu9fswIBEIBAHYTCSRVekmgf7Kn7IHxWcob9yxLUWe93eKa/+B9iebKprWQTP6bqpqDYd5BCMx73/Oix3iHA/svGVw4e5sfsJg=="
  - name: KLAVIYO_MARSLADDER_USERS_ALL_LIST
    value: "V2qx7h"
  - name: KMS_ENCRYPTED_FACEBOOK_APP_SECRET
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gFd4GWzX/cqWIFCb8lN8fRmAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM0RsbZSffh58VFlJYAgEQgDvnU1VYGcFEJrevxFspxqRZK1yVxVVrznOVT5IyhRqx3bIpBhXw2TP3YMEeJbvWD2CBu+jA2pJG/LnnPw=="
  - name: KMS_ENCRYPTED_FASTCOMMENTS_API_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gH64SuZg3M0pzgTq4gKkag/AAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMS2Z7m1YzoXHfxlbEAgEQgDtIG5zu1SzwgTOW3eo0uDtVRrTBEFaQv1Dbl+ja4RRzVkekiaVBTPyUXc0Ck90U+Adug8+oOTrzZDLy/A=="
  - name: FASTCOMMENTS_TENANT_ID
    value: "XSrXJR8zx2k"
  - name: SUBSCRIPTION_BUNDLE_TRIAL_COURSE_ENABLED
    value: "true"
