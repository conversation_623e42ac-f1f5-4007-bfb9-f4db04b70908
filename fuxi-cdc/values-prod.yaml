env: prod

serviceAccount:
  roleARN: arn:aws:iam::************:role/fuxi-cdc-service-role

resources:
  requests:
    memory: 1Gi
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

environments:
  - name: BOOTSTRAP_SERVERS
    value: "b1.p.marsladder.com.au:9092,b2.p.marsladder.com.au:9092,b3.p.marsladder.com.au:9092"
  - name: GROUP_ID
    value: "1"
  - name: CONFIG_STORAGE_TOPIC
    value: ml_connect_configs
  - name: OFFSET_STORAGE_TOPIC
    value: ml_connect_offsets
  - name: STATUS_STORAGE_TOPIC
    value: ml_source_connect_statuses
  - name: HOSTNAME
    value: "db.p.marsladder.com.au"
  - name: USERNAME
    value: "ladygaga"
  - name: KMS_ENCRYPTED_PASSWORD
    value: "AQICAHgM7zdc+obqmlNZ11n/tPKcerDrT/9r+lti5PLwmUOJmwGxEgtnCJdmNtj6ZgIz+i05AAAAdDByBgkqhkiG9w0BBwagZTBjAgEAMF4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMMxGAWPkArcB9ZQT9AgEQgDEmMJt93CRegBG4W2YlaGM5jWThNp1lhbU1Gnlh5AAZDDn1Wtfdqvxw9fY4VUU/wOXF"
