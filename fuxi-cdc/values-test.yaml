env: test

serviceAccount:
  roleARN: arn:aws:iam::************:role/fuxi-cdc-service-role

resources:
  requests:
    memory: 1Gi
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

environments:
  - name: BOOTSTRAP_SERVERS
    value: "b1.test.marsladder.com.au:9092,b2.test.marsladder.com.au:9092,b3.test.marsladder.com.au:9092"
  - name: GROUP_ID
    value: "1"
  - name: CONFIG_STORAGE_TOPIC
    value: ml_connect_configs
  - name: OFFSET_STORAGE_TOPIC
    value: ml_connect_offsets
  - name: STATUS_STORAGE_TOPIC
    value: ml_source_connect_statuses
  - name: HOSTNAME
    value: "db.test.marsladder.com.au"
  - name: USERNAME
    value: "ladygaga"
  - name: KMS_ENCRYPTED_PASSWORD
    value: "AQICAHi606znfP0KI/InEC6K/Hjy+pJCsCxVBn7Oqf8CyOrLqwH7NzTkOhrzSeWvE7+wNaEIAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMTBpRrBEibNoEPF2pAgEQgC/lRF/hAGWQJuTgLp2LN0+qY4MpkKsJW9bNKq8KzeBn8/OeQNdJOwZQJrgI1lETjA=="
