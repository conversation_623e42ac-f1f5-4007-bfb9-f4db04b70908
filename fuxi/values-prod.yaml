env: prod
hosts:
  - fuxi.p.marsladder.com.au
  - fuxi.p.marsladder.com
  - fuxi.marsladder.com.au
  - fuxi.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/fuxi-service-role

autoscaling:
  minReplicas: 2
  maxReplicas: 20

resources:
  requests:
    memory: 4Gi
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 200
  memThreshold: 80

environments:
  - name: JAVA_TOOL_OPTIONS
    value: "-XX:MaxMetaspaceSize=1024m"
  - name: SPRING_PROFILES_ACTIVE
    value: prod
  - name: SPRING_DATASOURCE_URL
    value: *********************************************
  - name: SPRING_DATASOURCE_USERNAME
    value: fuxi
  - name: KMS_ENCRYPTED_SPRING_DATASOURCE_PASSWORD
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAFMTsf5i9lMe4j45Zu0QYPHAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMm+6BHV1hw5Ol77xoAgEQgC+QkUE0r7Z0NQcp4GrurR4j3/gj2vDJ+XXYsRG18FX7jsOS01ohgl6sU9/z+fcxoQ=="
  - name: NEW_RELIC_APP_NAME
    value: "FuXi(Prod)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEfOs8Km23SftKVy7jVRsdYAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOIdhGxgVjqkTeq6HAIBEIBDg/7F0akgGMlUWsFsveZ3U/Cbmbf4Cfk40EjgFckQJ5QYYnSVIxlkm6xu74udLhlo60ebrqog2PPz1xtmq/7bODhYDQ=="
