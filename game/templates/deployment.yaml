apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Chart.Name }}
  labels:
    app: {{ .Chart.Name }}
    release: "{{ .Values.image.tag }}"
spec:
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: {{ .Chart.Name }}
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}
        release: "{{ .Values.image.tag }}"
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
            requests:
              memory: 100Mi
              cpu: 100m
            limits:
              memory: 500Mi
              cpu: 500m
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: {{ index .Values.hosts 0 }}
              path: /vvvv.html
              port: http
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: {{ index .Values.hosts 0 }}
              path: /vvvv.html
              port: http
