env: test
hosts:
  - nezha.test.marsladder.com.au
  - nezha.test.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/nezha-service-role

autoscaling:
  minReplicas: 1
  maxReplicas: 2

resources:
  requests:
    memory: 2Gi
    cpu: 500m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 400
  memThreshold: 300

environments:
  - name: JAVA_TOOL_OPTIONS
    value: "-XX:MaxMetaspaceSize=1024m"
  - name: SPRING_PROFILES_ACTIVE
    value: test
  - name: SPRING_DATASOURCE_URL
    value: *************************************************
  - name: SPRING_DATASOURCE_USERNAME
    value: flyway
  - name: KMS_ENCRYPTED_SPRING_DATASOURCE_PASSWORD
    value: "AQICAHhHMXezALbBYgngtozRS/iXtUB3VCMhVjChtgwecP1ugAH9p/58zP7oGrdGpUuNtbeNAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMXokwRGWoVpQL6LEsAgEQgC9JwaaRO1mIAq03YGiPP38BcQC/6fhvQ9JPoSywJ0wj/5ZPTkIzzBTsUEh78YEwlA=="
  - name: NEW_RELIC_APP_NAME
    value: "NeZha(Test)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gF7H4sUYgyatDHyhHN4X0ouAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDNXBxv2TleaFWlDiVwIBEIBD3aVirGsENXnxFdzgWp3E8hVAaHQwcht62EGAfh3efArO7Gy+5aabWy85vyksIumbiCoNx/+gMxaINEFvVfXDDNBxgA=="
  - name: KMS_ENCRYPTED_SPRING_AI_OPENAI_API_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gFaNdi0VO1u7HJ6omslSpKoAAAAlDCBkQYJKoZIhvcNAQcGoIGDMIGAAgEAMHsGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM/6YwAqSAH9BeyELJAgEQgE6UERhkt/qplbrwfPUodP5Dw3xVJbikEpIFShJox+B+ZBAqmeYjdCE1NPPFag15ZsQOtw92TQMmpYzDLxwfkW9zyiCTu+6/Ge+h3VAlF8o="
