apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Chart.Name }}
  labels:
    app: {{ .Chart.Name }}
    release: "{{ .Values.image.tag }}"
spec:
  replicas: {{ .Values.replicaCount }}
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: {{ .Chart.Name }}
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}
        release: "{{ .Values.image.tag }}"
    spec:
      serviceAccountName: {{ .Chart.Name }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
            requests:
              memory: 2Gi
              cpu: 100m
            limits:
              memory: 8Gi
              cpu: 1
          env:
            - name: GIT_HASH
              value: "{{ .Values.image.tag }}"
          {{- with .Values.environments }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 60
            httpGet:
              path: /actuator/health
              port: 8080
          readinessProbe:
            initialDelaySeconds: 60
            httpGet:
              path: /actuator/health
              port: 8080
