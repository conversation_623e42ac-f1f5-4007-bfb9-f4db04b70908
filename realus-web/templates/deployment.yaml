apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ .Chart.Name }}-au"
  labels:
    app: "{{ .Chart.Name }}-au"
    release: "{{ .Values.image.tag }}"
spec:
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: "{{ .Chart.Name }}-au"
  template:
    metadata:
      labels:
        app: "{{ .Chart.Name }}-au"
        release: "{{ .Values.image.tag }}"
    spec:
      containers:
        - name: "{{ .Chart.Name }}-au"
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: "{{ .Values.image.pullPolicy }}"
          resources:
            requests:
              memory: 100Mi
              cpu: 100m
            limits:
              memory: 1Gi
              cpu: 500m
          env:
          {{- with .Values.environments }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 60
            httpGet:
              httpHeaders:
                - name: Host
                  value: "{{ index .Values.hosts.au 0 }}"
              path: /vvvv.html
              port: http
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: "{{ index .Values.hosts.au 0 }}"
              path: /vvvv.html
              port: http
