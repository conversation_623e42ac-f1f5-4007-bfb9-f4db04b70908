{{- if .Values.firstedu }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Chart.Name }}-firstedu
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.redirect: >
      {"Type":"redirect","RedirectConfig":{"Host":"{{ .Values.firstedu.redirectTo }}","Path":"/","Port":"443","Protocol":"HTTPS","Query":"originalSource=firstedu","StatusCode":"HTTP_301"}}
spec:
  rules:
    {{- range .Values.firstedu.hosts }}
  - http:
      paths:
      - path: /
        pathType: ImplementationSpecific
        backend:
          service:
            name: redirect
            port:
              name: use-annotation
    host: {{ . }}
    {{- end }}
{{- end }}
