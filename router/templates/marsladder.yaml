{{- if .Values.marsladder }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Chart.Name }}-marsladder
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: "marsladder-{{ .Values.env }}"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/actions.redirect: >
      {"Type":"redirect","RedirectConfig":{"Host":"{{ .Values.marsladder.redirectTo }}","Path":"/#{path}","Port":"443","Protocol":"HTTPS","Query":"#{query}","StatusCode":"HTTP_301"}}
spec:
  rules:
    {{- range .Values.marsladder.hosts }}
  - http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: redirect
            port:
              name: use-annotation
    host: {{ . }}
    {{- end }}
{{- end }}
