env: prod
hosts:
  - user-service.marsladder.com.au
  - user-service.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/user-service-role

autoscaling:
  minReplicas: 2
  maxReplicas: 20

resources:
  requests:
    memory: 4Gi
    cpu: 250m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 200
  memThreshold: 80

environments:
  - name: JAVA_TOOL_OPTIONS
    value: "-XX:MaxMetaspaceSize=1024m"
  - name: SPRING_PROFILES_ACTIVE
    value: prod
  - name: NEW_RELIC_APP_NAME
    value: "User-Service(Prod)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAEfOs8Km23SftKVy7jVRsdYAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOIdhGxgVjqkTeq6HAIBEIBDg/7F0akgGMlUWsFsveZ3U/Cbmbf4Cfk40EjgFckQJ5QYYnSVIxlkm6xu74udLhlo60ebrqog2PPz1xtmq/7bODhYDQ=="
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_SECRET
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAH5Jowb1iLigjvwM70as9a3AAAAzjCBywYJKoZIhvcNAQcGoIG9MIG6AgEAMIG0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDPzdcuXXLDcRR9w/mAIBEICBhrMsSdV4WXIMwRTsanTL9BqSkU8QsWVvY/7/ZApGMxIoQk7OC6KH5yz1LhhCuF3tInUZK2t9feXk/2OHEst4onfKI6nN9SwgeHVDhSR/boK14bzOqEs4cb10PxFjBfff9xdk5RuH9QeppYMONaa6DlB+UcvPNSwU1iGzLiX6V7DJVoSITDWY"
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_WEBHOOK_SECRET
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAFKKqn2VyGVurrTsR33REd4AAAAhTCBggYJKoZIhvcNAQcGoHUwcwIBADBuBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDDJ+d1EDq4MXY6jkBAIBEIBBNKandafH5ZYbZLwO/cRq7nJ7x6jXRkNXt9KngKwK97EQev1eY2mGuCW1ebiLxMF53jHmIVshFTU/owWunOptmYw="
  - name: SPRING_DATASOURCE_USERNAME
    value: "user_service"
  - name: KMS_ENCRYPTED_SPRING_DATASOURCE_PASSWORD
    value: "AQICAHh4w6uFp4gZfesRn5AMJbsu9uNVmMwhp9M5lkL0uy2MqAFQCXKDWYYgeAMZeLlg+BRqAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMPcmKcrek7cAKcRp2AgEQgC+8bI0/Iw6sSOr1uxrDR01T5UltymsZusVuFjNPgsu31PDx862f7Zzhm9yD/S0lZA=="
