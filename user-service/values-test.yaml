env: test
hosts:
  - user-service.test.marsladder.com.au
  - user-service.test.marsladder.com
alb:
serviceAccount:
  roleARN: arn:aws:iam::************:role/user-service-role

autoscaling:
  minReplicas: 1
  maxReplicas: 2

resources:
  requests:
    memory: 2Gi
    cpu: 500m
  limits:
    memory: 8Gi
    cpu: 2

hpa:
  cpuThreshold: 400
  memThreshold: 300

environments:
  - name: JAVA_TOOL_OPTIONS
    value: "-XX:MaxMetaspaceSize=1024m"
  - name: SPRING_PROFILES_ACTIVE
    value: test
  - name: NEW_RELIC_APP_NAME
    value: "User-Service(Test)"
  - name: KMS_ENCRYPTED_NEW_RELIC_LICENSE_KEY
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gF7H4sUYgyatDHyhHN4X0ouAAAAhzCBhAYJKoZIhvcNAQcGoHcwdQIBADBwBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDNXBxv2TleaFWlDiVwIBEIBD3aVirGsENXnxFdzgWp3E8hVAaHQwcht62EGAfh3efArO7Gy+5aabWy85vyksIumbiCoNx/+gMxaINEFvVfXDDNBxgA=="
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_SECRET
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gHko+mKZvaFl5sYwxgg+HX4AAAAzjCBywYJKoZIhvcNAQcGoIG9MIG6AgEAMIG0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDGDsvifOw6VT+D8BzgIBEICBhk5QU1lK6dtBSkA6P+vFS1qUERgOJ/yCQLWUb659fF097ZgmsKgCONTEWN/caZZ7YHlpbIMQIfKngbH5krQHzTYFb++Lwwzk+2hulh7vImipVJDVgdGrmxZHMRnvCn9FJ5opQmN3YFD5LuCleTx6ZsdeB33Au7a3OBYb9LrsPHBm0BfPoMVI"
  - name: KMS_ENCRYPTED_SUBSCRIPTION_STRIPE_WEBHOOK_SECRET
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gGF8IGb03bqUvoRcYNFqCkLAAAAhTCBggYJKoZIhvcNAQcGoHUwcwIBADBuBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDHccpdA/Rs4GH/12PQIBEIBBfnfQ284MjNgjd9G/g7+GHQprD3ZHJk9dGfsC767HvueF10Fbh45040LIYiqz/q6eXuM1GV73qQJ8gK6pYD8BBA4="
  - name: SPRING_DATASOURCE_USERNAME
    value: "user_service"
  - name: KMS_ENCRYPTED_SPRING_DATASOURCE_PASSWORD
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gGyPK8pJ+gzBzMXVvRa9uabAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM/5ToJ2gFSnMAnFGdAgEQgC+DXRRI8ijkjZLKpHNkJmiI8TsIPJgoyqjZ0+7B33nl4Q0frqxfQEUkhGG3PgE29A=="
