env: test
hosts:
  au:
    - test.marsladder.com.au
    - www.test.marsladder.com.au
    - api.test.marsladder.com.au
  com:
    - test.marsladder.com
    - www.test.marsladder.com
alb:
  auth:
    userPoolClientID: 49ehnhud8d6aekdssc4llk55ou
    userPoolDomain: sso.test.marsladder.com.au

oldVersionWeight: 0

autoscaling:
  minReplicas: 1
  maxReplicas: 2

hpa:
  cpuThreshold: 500
  memThreshold: 900

environments:
  - name: META_PIXEL_KEY
    value: "****************"
  - name: GOOGLE_TAG_MANAGER_KEY
    value: GTM-NZ4NR39
  - name: APP_ENV
    value: test
  - name: FAST_COMMENTS_TENANT_ID
    value: XSrXJR8zx2k
  - name: GROWTHBOOK_CLIENT_KEY
    value: sdk-z2zmqvOYvcoT86Rc
  - name: TIKTOK_EVENT_API_TOKEN
    value: 89899bd475667c51addcfa3992e3a42785e59533
  - name: NEW_RELIC_ACCOUNT_ID
    value: "3308161"
  - name: NEW_RELIC_APPLICATION_ID
    value: "**********"
  - name: NEW_RELIC_LICENSE_KEY
    value: NRJS-a8789cb21748e4c1c30