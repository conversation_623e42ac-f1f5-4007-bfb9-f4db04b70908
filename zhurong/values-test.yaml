env: test
serviceAccount:
  roleARN: arn:aws:iam::************:role/zhurong-service-role

environments:
  - name: SPRING_PROFILES_ACTIVE
    value: test
  - name: FEIGN_FALCON_URL
    value: https://api.test.marsladder.com.au
  - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_ZHURONG_PROVIDER
    value: cognito
  - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_ZHURONG_CLIENT_ID
    value: 7hci689m9llcd5389l2sqn1cra
  - name: KMS_ENCRYPTED_SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_ZHURONG_CLIENT_SECRET
    value: "AQICAHi5p8+70Ks6bhnul1LUSLBePzW0WvwiX5WWer1r0/DD4gGYe1RkdQMuk42YJOim65/SAAAAlTCBkgYJKoZIhvcNAQcGoIGEMIGBAgEAMHwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMVeMS/YwjcoAPYViYAgEQgE8yJjgMC0wt+UxzxtdY/Xy3KJmGYtL84pwDl/L/k8JkDbSLryrtdAPctJIikAzmf5boMIHtS4zDd7SR8TYKGIJtAhZjMjurM4HARDNJqaCp"
  - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_ZHURONG_AUTHORIZATION_GRANT_TYPE
    value: client_credentials
  - name: SPRING_SECURITY_OAUTH2_CLIENT_PROVIDER_COGNITO_ISSUER_URI
    value: https://cognito-idp.ap-southeast-2.amazonaws.com/ap-southeast-2_5XijcEWvj
